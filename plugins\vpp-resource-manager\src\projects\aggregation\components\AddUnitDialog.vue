<template>
  <CetDialog
    v-bind="CetDialog_addUnit"
    v-on="CetDialog_addUnit.event"
    class="add-unit-dialog"
  >
    <div class="dialog-content">
      <!-- 基本信息表单 -->
      <div class="form-section">
        <CetForm
          :data.sync="CetForm_addUnit.data"
          v-bind="CetForm_addUnit"
          v-on="CetForm_addUnit.event"
        >
          <el-row :gutter="24">
            <!-- 机组名称 -->
            <el-col :span="8">
              <el-form-item label="机组名称" prop="unitName">
                <ElInput
                  v-model="CetForm_addUnit.data.unitName"
                  placeholder="请输入内容"
                />
              </el-form-item>
            </el-col>

            <!-- 机组类型 -->
            <el-col :span="8">
              <el-form-item label="机组类型" prop="unitType">
                <ElSelect
                  v-model="CetForm_addUnit.data.unitType"
                  placeholder="请选择"
                  multiple
                  collapse-tags
                  @change="handleUnitTypeChange"
                >
                  <ElOption
                    v-for="item in unitTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </ElSelect>
              </el-form-item>
            </el-col>
          </el-row>
        </CetForm>
      </div>

      <!-- 资源选择区域 - 只有选择了机组类型才显示 -->
      <div class="resource-section">
        <span class="resource-title">调峰机组资源列表</span>

        <!-- 搜索和筛选 -->
        <div class="resource-filter">
          <div class="filter-row">
            <!-- 搜索框 -->
            <ElInput
              style="width: 290px"
              v-model="resourceSearch"
              placeholder="请输入关键字"
              prefix-icon="el-icon-search"
              @input="handleResourceSearch"
            />
            <!-- 区域筛选 -->
            <CustomElSelect
              v-model="selectedArea"
              :prefix_in="'区域'"
              @change="handleAreaChange"
            >
              <ElOption
                v-for="item in areaOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </CustomElSelect>
          </div>
        </div>

        <!-- 资源表格 -->
        <div class="resource-table">
          <CetTable
            :data.sync="CetTable_resources.data"
            v-bind="CetTable_resources"
            v-on="CetTable_resources.event"
            @selection-change="handleResourceSelection"
            :border="false"
          >
            <!-- 多选列 -->
            <ElTableColumn
              type="selection"
              width="50"
              align="center"
              header-align="center"
            />

            <!-- 序号列 -->
            <ElTableColumn v-bind="ElTableColumn_index" />

            <!-- 资源ID列 -->
            <ElTableColumn v-bind="ElTableColumn_resourceId" />

            <!-- 资源名称列 -->
            <ElTableColumn v-bind="ElTableColumn_resourceName" />

            <!-- 区域列 -->
            <ElTableColumn v-bind="ElTableColumn_area" />

            <!-- 容量列 -->
            <ElTableColumn v-bind="ElTableColumn_capacity" />

            <!-- 平台直控列 -->
            <ElTableColumn v-bind="ElTableColumn_directControl" />
          </CetTable>

          <!-- 分页器 -->
          <div class="pagination-wrapper">
            <div class="pagination-total">
              共
              <span class="total-number">{{ total }}</span>
              个
            </div>
            <el-pagination
              class="pagination-container"
              background
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 15, 20, 50, 100]"
              :page-size="pageSize"
              :total="total"
              layout="sizes, prev, pager, next, jumper"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
        class="cancel-button"
      />
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
        class="confirm-button"
      />
    </span>
  </CetDialog>
</template>

<script>
export default {
  name: "AddUnitDialog",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number,
      default: 0
    },
    closeTrigger_in: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      // 弹窗配置
      CetDialog_addUnit: {
        title: "新增",
        width: "960px",
        he
        openTrigger_in: 0,
        closeTrigger_in: 0,
        showClose: true,
        "append-to-body": true,
        event: {
          openTrigger_out: this.handleDialogOpen,
          closeTrigger_out: this.handleDialogClose
        }
      },

      // 表单组件配置
      CetForm_addUnit: {
        dataMode: "component",
        queryMode: "trigger",
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          groups: []
        },
        inputData_in: {},
        data: {
          unitName: "",
          unitType: []
        },
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        labelWidth: "120px",
        labelPosition: "top",
        rules: {
          unitName: [
            { required: true, message: "请输入机组名称", trigger: "blur" }
          ],
          unitType: [
            {
              required: true,
              type: "array",
              min: 1,
              message: "请至少选择一个机组类型",
              trigger: "change"
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_addUnit_saveData_out
        }
      },

      // 机组类型选项
      unitTypeOptions: [
        { value: "peaking", label: "调峰机组" },
        { value: "thermal", label: "火电机组" },
        { value: "hydro", label: "水电机组" },
        { value: "wind", label: "风电机组" },
        { value: "solar", label: "光伏机组" }
      ],

      // 资源搜索和筛选
      resourceSearch: "",
      selectedArea: "",
      areaOptions: [
        { value: "", label: "全部" },
        { value: "江苏", label: "江苏" },
        { value: "浙江", label: "浙江" },
        { value: "上海", label: "上海" },
        { value: "安徽", label: "安徽" }
      ],

      // 资源表格配置
      CetTable_resources: {
        data: [],
        queryMode: "component",
        dataMode: "component",
        queryTrigger_in: new Date().getTime(),
        showPagination: false,
        event: {}
      },

      // 分页数据
      allResourcesData: [],
      filteredResourcesData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,

      // 选中的资源
      selectedResources: [],

      // 表格列配置
      ElTableColumn_index: {
        type: "index",
        label: "序号",
        width: "72",
        align: "center",
        headerAlign: "center"
      },

      ElTableColumn_resourceId: {
        prop: "resourceId",
        label: "资源ID",
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true
      },

      ElTableColumn_resourceName: {
        prop: "resourceName",
        label: "资源名称",
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },

      ElTableColumn_area: {
        prop: "area",
        label: "区域",
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true
      },

      ElTableColumn_capacity: {
        prop: "capacity",
        label: "报装容量(kVA)",
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true
      },

      ElTableColumn_directControl: {
        prop: "directControl",
        label: "平台直控",
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true
      },

      // 按钮配置
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "取消",
        type: "default",
        plain: true,
        event: {
          statusTrigger_out: this.handleCancel
        }
      },

      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: "确定",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.handleConfirm
        }
      }
    };
  },

  watch: {
    visibleTrigger_in(val) {
      if (val) {
        this.CetDialog_addUnit.openTrigger_in = val;
        this.initDialog();
      }
    },

    closeTrigger_in(val) {
      if (val) {
        this.CetDialog_addUnit.closeTrigger_in = val;
      }
    }
  },

  methods: {
    // 初始化弹窗
    initDialog() {
      this.resetForm();
      // 不在这里初始化资源数据，等待用户选择机组类型后再初始化
    },

    // 重置表单
    resetForm() {
      this.CetForm_addUnit.data = {
        unitName: "",
        unitType: []
      };
      this.CetForm_addUnit.resetTrigger_in = new Date().getTime();
      this.resourceSearch = "";
      this.selectedArea = "";
      this.selectedResources = [];
      // 清空资源数据
      this.allResourcesData = [];
      this.filteredResourcesData = [];
      this.CetTable_resources.data = [];
      this.total = 0;
      this.currentPage = 1;
    },

    // 初始化资源数据
    initResourceData() {
      console.log("正在生成模拟资源数据...");
      // 生成模拟资源数据
      this.allResourcesData = this.generateMockResourceData();
      this.filteredResourcesData = [...this.allResourcesData];
      this.total = this.filteredResourcesData.length;
      this.currentPage = 1;
      console.log("生成的资源数据总数:", this.allResourcesData.length);
      console.log("过滤后的资源数据总数:", this.filteredResourcesData.length);
      this.updateResourceTableData();
    },

    // 生成模拟资源数据 - 根据Figma原型数据
    generateMockResourceData() {
      const mockData = [
        {
          id: 1,
          resourceId: "9144030078525478X0",
          resourceName: "华能海门电厂#1机组",
          area: "江苏",
          capacity: "660.0",
          directControl: "是",
          status: "运行中",
          type: "火电"
        },
        {
          id: 2,
          resourceId: "9144030078525478X1",
          resourceName: "华能海门电厂#2机组",
          area: "江苏",
          capacity: "660.0",
          directControl: "是",
          status: "运行中",
          type: "火电"
        },
        {
          id: 3,
          resourceId: "9144030078525478X2",
          resourceName: "华能南通电厂#1机组",
          area: "江苏",
          capacity: "1000.0",
          directControl: "否",
          status: "运行中",
          type: "火电"
        },
        {
          id: 4,
          resourceId: "9144030078525478X3",
          resourceName: "华能南通电厂#2机组",
          area: "江苏",
          capacity: "1000.0",
          directControl: "是",
          status: "检修中",
          type: "火电"
        },
        {
          id: 5,
          resourceId: "9144030078525478X4",
          resourceName: "大唐南京电厂#1机组",
          area: "江苏",
          capacity: "600.0",
          directControl: "是",
          status: "运行中",
          type: "火电"
        },
        {
          id: 6,
          resourceId: "9144030078525478X5",
          resourceName: "大唐南京电厂#2机组",
          area: "江苏",
          capacity: "600.0",
          directControl: "否",
          status: "运行中",
          type: "火电"
        },
        {
          id: 7,
          resourceId: "9144030078525478X6",
          resourceName: "国电泰州电厂#1机组",
          area: "江苏",
          capacity: "1000.0",
          directControl: "是",
          status: "运行中",
          type: "火电"
        },
        {
          id: 8,
          resourceId: "9144030078525478X7",
          resourceName: "国电泰州电厂#2机组",
          area: "江苏",
          capacity: "1000.0",
          directControl: "是",
          status: "运行中",
          type: "火电"
        },
        {
          id: 9,
          resourceId: "9144030078525478X8",
          resourceName: "华电句容电厂#1机组",
          area: "江苏",
          capacity: "660.0",
          directControl: "否",
          status: "停机",
          type: "火电"
        },
        {
          id: 10,
          resourceId: "9144030078525478X9",
          resourceName: "华电句容电厂#2机组",
          area: "江苏",
          capacity: "660.0",
          directControl: "是",
          status: "运行中",
          type: "火电"
        },
        {
          id: 11,
          resourceId: "9144030078525478Y0",
          resourceName: "中电投滨海风电场",
          area: "江苏",
          capacity: "200.0",
          directControl: "是",
          status: "运行中",
          type: "风电"
        },
        {
          id: 12,
          resourceId: "9144030078525478Y1",
          resourceName: "华能大丰风电场",
          area: "江苏",
          capacity: "300.0",
          directControl: "否",
          status: "运行中",
          type: "风电"
        },
        {
          id: 13,
          resourceId: "9144030078525478Y2",
          resourceName: "国电如东风电场",
          area: "江苏",
          capacity: "150.0",
          directControl: "是",
          status: "运行中",
          type: "风电"
        },
        {
          id: 14,
          resourceId: "9144030078525478Y3",
          resourceName: "三峡新能源射阳风电",
          area: "江苏",
          capacity: "250.0",
          directControl: "是",
          status: "运行中",
          type: "风电"
        },
        {
          id: 15,
          resourceId: "9144030078525478Y4",
          resourceName: "协鑫光伏电站",
          area: "江苏",
          capacity: "100.0",
          directControl: "否",
          status: "运行中",
          type: "光伏"
        },
        {
          id: 16,
          resourceId: "9144030078525478Y5",
          resourceName: "天合光能电站",
          area: "江苏",
          capacity: "80.0",
          directControl: "是",
          status: "运行中",
          type: "光伏"
        },
        {
          id: 17,
          resourceId: "9144030078525478Y6",
          resourceName: "阿特斯光伏电站",
          area: "江苏",
          capacity: "120.0",
          directControl: "是",
          status: "运行中",
          type: "光伏"
        },
        {
          id: 18,
          resourceId: "9144030078525478Y7",
          resourceName: "晶科能源电站",
          area: "江苏",
          capacity: "90.0",
          directControl: "否",
          status: "运行中",
          type: "光伏"
        },
        {
          id: 19,
          resourceId: "9144030078525478Y8",
          resourceName: "华能扬州储能站",
          area: "江苏",
          capacity: "50.0",
          directControl: "是",
          status: "运行中",
          type: "储能"
        },
        {
          id: 20,
          resourceId: "9144030078525478Y9",
          resourceName: "国网南京储能站",
          area: "江苏",
          capacity: "100.0",
          directControl: "是",
          status: "运行中",
          type: "储能"
        },
        {
          id: 21,
          resourceId: "9144030078525478Z0",
          resourceName: "华电常州电厂#1机组",
          area: "江苏",
          capacity: "600.0",
          directControl: "否",
          status: "运行中",
          type: "火电"
        },
        {
          id: 22,
          resourceId: "9144030078525478Z1",
          resourceName: "华电常州电厂#2机组",
          area: "江苏",
          capacity: "600.0",
          directControl: "是",
          status: "运行中",
          type: "火电"
        },
        {
          id: 23,
          resourceId: "9144030078525478Z2",
          resourceName: "大唐连云港电厂#1机组",
          area: "江苏",
          capacity: "660.0",
          directControl: "是",
          status: "运行中",
          type: "火电"
        },
        {
          id: 24,
          resourceId: "9144030078525478Z3",
          resourceName: "大唐连云港电厂#2机组",
          area: "江苏",
          capacity: "660.0",
          directControl: "否",
          status: "检修中",
          type: "火电"
        },
        {
          id: 25,
          resourceId: "9144030078525478Z4",
          resourceName: "国电徐州电厂#1机组",
          area: "江苏",
          capacity: "600.0",
          directControl: "是",
          status: "运行中",
          type: "火电"
        }
      ];

      // 扩展更多区域的数据
      const additionalAreas = ["浙江", "上海", "安徽"];
      const baseData = [...mockData];

      additionalAreas.forEach((area, areaIndex) => {
        for (let i = 1; i <= 15; i++) {
          const id = baseData.length + areaIndex * 15 + i;
          const types = ["火电", "风电", "光伏", "储能"];
          const type = types[Math.floor(Math.random() * types.length)];
          const statuses = ["运行中", "检修中", "停机"];
          const status = statuses[Math.floor(Math.random() * statuses.length)];
          const directControlOptions = ["是", "否"];
          const directControl =
            directControlOptions[
              Math.floor(Math.random() * directControlOptions.length)
            ];

          let capacity;
          switch (type) {
            case "火电":
              capacity = (Math.random() * 400 + 600).toFixed(1);
              break;
            case "风电":
              capacity = (Math.random() * 200 + 100).toFixed(1);
              break;
            case "光伏":
              capacity = (Math.random() * 100 + 50).toFixed(1);
              break;
            case "储能":
              capacity = (Math.random() * 80 + 30).toFixed(1);
              break;
            default:
              capacity = (Math.random() * 100 + 50).toFixed(1);
          }

          // 生成符合Figma设计的资源ID格式
          const resourceIdSuffix =
            String.fromCharCode(65 + areaIndex) + i.toString().padStart(2, "0");

          baseData.push({
            id: id,
            resourceId: `9144030078525478${resourceIdSuffix}`,
            resourceName: `${area}${type}资源${i}`,
            area: area,
            capacity: capacity,
            directControl: directControl,
            status: status,
            type: type
          });
        }
      });

      return baseData;
    },

    // 更新资源表格数据
    updateResourceTableData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      this.CetTable_resources.data = this.filteredResourcesData.slice(
        start,
        end
      );
      // 触发表格更新
      this.CetTable_resources.queryTrigger_in = new Date().getTime();
    },

    // 过滤资源数据
    filterResourceData() {
      const searchKeyword = this.resourceSearch.toLowerCase();
      const selectedArea = this.selectedArea;

      this.filteredResourcesData = this.allResourcesData.filter(item => {
        // 名称和ID过滤
        const nameMatch =
          !searchKeyword ||
          item.resourceName.toLowerCase().includes(searchKeyword) ||
          item.resourceId.toLowerCase().includes(searchKeyword);

        // 区域过滤
        const areaMatch = !selectedArea || item.area === selectedArea;

        return nameMatch && areaMatch;
      });

      // 重置分页
      this.currentPage = 1;
      this.total = this.filteredResourcesData.length;
      this.updateResourceTableData();
    },

    // 资源搜索处理
    handleResourceSearch() {
      this.filterResourceData();
    },

    // 机组类型变化处理
    handleUnitTypeChange(value) {
      console.log("机组类型变化:", value);
      // 当机组类型变化时，重新初始化资源数据
      if (value && value.length > 0) {
        console.log("开始初始化资源数据...");
        this.initResourceData();
        console.log(
          "资源数据初始化完成，表格数据长度:",
          this.CetTable_resources.data.length
        );
        console.log("总数据长度:", this.allResourcesData.length);
      } else {
        console.log("机组类型为空，清空资源数据");
        this.allResourcesData = [];
        this.filteredResourcesData = [];
        this.CetTable_resources.data = [];
        this.total = 0;
        this.CetTable_resources.queryTrigger_in = new Date().getTime();
      }
    },

    // 区域变化处理
    handleAreaChange() {
      this.filterResourceData();
    },

    // 分页大小变化
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.currentPage = 1;
      this.updateResourceTableData();
    },

    // 当前页变化
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage;
      this.updateResourceTableData();
    },

    // 资源选择变化
    handleResourceSelection(selection) {
      this.selectedResources = selection;
    },

    // 弹窗打开处理
    handleDialogOpen() {
      console.log("弹窗已打开");
    },

    // 弹窗关闭处理
    handleDialogClose() {
      this.$emit("closeTrigger_out", Date.now());
    },

    // 取消按钮处理
    handleCancel() {
      this.CetDialog_addUnit.closeTrigger_in = Date.now();
    },

    // 确定按钮处理
    handleConfirm() {
      // 触发表单保存验证
      this.CetForm_addUnit.localSaveTrigger_in = new Date().getTime();
    },

    // CetForm保存处理
    CetForm_addUnit_saveData_out(formData) {
      // 表单验证通过，构造提交数据
      const submitData = {
        unitName: formData.unitName.trim(),
        unitType: formData.unitType, // 现在是数组
        unitTypeLabels: formData.unitType.map(type => {
          const option = this.unitTypeOptions.find(opt => opt.value === type);
          return option ? option.label : type;
        }),
        selectedResources: this.selectedResources,
        resourceCount: this.selectedResources.length
      };

      // 触发保存事件
      this.$emit("confirm_out", submitData);

      // 关闭弹窗
      this.CetDialog_addUnit.closeTrigger_in = Date.now();

      this.$message.success("新增机组成功");
    }
  }
};
</script>

<style scoped>
.dialog-content {
  background-color: var(--BG1);
  border-radius: 8px;
  padding: 24px;
}

/* 表单区域 */
.form-section {
  margin-bottom: 24px;
}

.form-section :deep(.el-form-item__label) {
  font-size: 14px;
  color: var(--Text1);
  line-height: 22px;
}

.form-input,
.form-select {
  width: 100%;
}

/* 资源选择区域 */
.resource-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.resource-title {
  font-size: 14px;
  color: var(--Text1);
  font-weight: 400;
}

.resource-filter {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 16px;
}

.area-select {
  width: 240px;
}

.bind-button {
  margin-left: auto;
}

/* 资源表格 */
.resource-table {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 分页器包装器 */
.pagination-wrapper {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
}

.pagination-total {
  color: var(--Text2);
  font-size: 14px;
}

.pagination-total .total-number {
  color: var(--ZS);
  font-weight: 500;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
}

/* 底部按钮 */
.cancel-button {
  margin-right: 8px;
}

.confirm-button {
  margin-left: 8px;
}
</style>
